import { create } from "zustand";
import { persist } from "zustand/middleware";
import { useBookingStore } from "./booking-store";

// User data types based on the existing API structure
export interface UserEmail {
  email: string;
}

export interface UserPhone {
  phone: string;
  country_code: string;
}

export interface ProfileImage {
  path: string;
}

export interface UserCurrentPackage {
  id: number;
  name: string;
  is_default: boolean;
  corporate_email_limit: number;
}

export interface ActivePurchaseSubscription {
  start_date: string;
  end_date: string;
}

export interface User {
  id: number;
  dob?: string;
  profile_image?: ProfileImage;
  user_email?: UserEmail;
  user_phone?: UserPhone;
  userCurrentPackage?: UserCurrentPackage;
  active_purchase_subscription?: ActivePurchaseSubscription;
  name?: string;
  is_corporate_root_user: boolean;
  is_corporate_user: boolean;
  wallet_amount: string;
}

export interface AuthResponse {
  status: string;
  message?: string;
  token: string;
  user: User;
}

export interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  login: (authData: AuthResponse) => void;
  setToken: (token: string) => void;
  setUser: (user: User) => void;
  removeToken: () => void;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
  checkAuth: () => Promise<void>;
  initializeAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,

      // Login action
      login: (authData: AuthResponse) => {
        const { token, user } = authData;

        // Store in localStorage for backward compatibility
        localStorage.setItem("token", token);
        localStorage.setItem("user", JSON.stringify(authData));

        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        });
      },

      // Logout action
      logout: () => {
        // Clear localStorage
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        localStorage.removeItem("epic-padel-booking-store");
        localStorage.removeItem("epic-padel-checkout-store");
        localStorage.removeItem("epic-padel-subscription-store");

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      setToken: (token: string) => {
        set({ token });
        localStorage.setItem("token", token);
      },

      setUser: (user: User) => {
        set({ user });
        localStorage.setItem("user", JSON.stringify(user));
        get().checkAuth();
      },

      removeToken: () => {
        set({ token: null });
        localStorage.removeItem("token");
      },

      // Update user data
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData };

          // Update localStorage
          const currentAuthData = localStorage.getItem("user");
          if (currentAuthData) {
            const authData = JSON.parse(currentAuthData);
            authData.user = updatedUser;
            localStorage.setItem("user", JSON.stringify(authData));
          }

          set({ user: updatedUser });
        }
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Check authentication status
      checkAuth: async () => {
        set({ isLoading: true });

        try {
          const token = localStorage.getItem("token");
          const userString = localStorage.getItem("user");

          if (token && userString) {
            // Validate token with auto-login endpoint
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL}/mob_app/auth/autoLogin`,
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.ok) {
              const userData = await response.json();

              // Update stored user data
              localStorage.setItem("user", JSON.stringify(userData));

              set({
                user: userData.user,
                token,
                isAuthenticated: true,
                isLoading: false,
              });
            } else {
              // Token is invalid, clear auth data
              get().logout();

            }
          } else {
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
            });
          }
        } catch (error) {
          console.error("Auth check failed:", error);
          get().logout();
        }
      },

      // Initialize auth on app start
      initializeAuth: () => {
        const token = localStorage.getItem("token");
        const userString = localStorage.getItem("user");

        if (token && userString) {
          try {
            const authData = JSON.parse(userString);
            set({
              user: authData.user,
              token,
              isAuthenticated: true,
              isLoading: false,
            });
          } catch (error) {
            console.error("Failed to parse stored user data:", error);
            get().logout();
          }
        } else {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
