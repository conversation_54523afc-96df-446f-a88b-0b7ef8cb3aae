import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { Plan } from "@/types/membership";
import { PersonalInfo, PaymentMethod } from "@/components/subscription/types";

/**
 * Subscription Store with Persistence
 *
 * This store persists subscription data to localStorage to maintain state across page refreshes.
 * The persisted data includes:
 * - selectedPlan: The selected membership plan
 * - personalInfo: User's personal information
 * - selectedPaymentMethod: Selected payment method
 * - currentStep: Current step in the subscription flow
 *
 * UI state (isProcessing, error) is not persisted and will reset on page refresh.
 */

export interface SubscriptionState {
    // Persisted state
    selectedPlan: Plan | null;
    personalInfo: PersonalInfo;
    selectedPaymentMethod: PaymentMethod | null;
    currentStep: number;
    packageAmount: number;
    isMonthly: boolean;

    // Non-persisted UI state
    isProcessing: boolean;
    error: string | null;

    // Actions
    setSelectedPlan: (plan: Plan | null) => void;
    setPersonalInfo: (info: PersonalInfo) => void;
    updatePersonalInfo: (updates: Partial<PersonalInfo>) => void;
    setSelectedPaymentMethod: (method: PaymentMethod | null) => void;
    setCurrentStep: (step: number) => void;
    setPackageAmount: (amount: number) => void;
    setIsMonthly: (isMonthly: boolean) => void;
    nextStep: () => void;
    previousStep: () => void;
    setProcessing: (processing: boolean) => void;
    setError: (error: string | null) => void;
    resetSubscription: () => void;
    canProceedToNextStep: () => boolean;
    populateUserData: (userData: {
        name?: string;
        email?: string;
        phone?: string;
    }) => void;
}

const initialPersonalInfo: PersonalInfo = {
    name: "",
    mobile: "",
    email: "",
    countryCode: "+1",
    hasAgreedToWaiver: false,
    isAbove18: false,
};

export const useSubscriptionStore = create<SubscriptionState>()(
    // persist(
    (set, get) => ({
        // Initial persisted state
        selectedPlan: null,
        personalInfo: initialPersonalInfo,
        selectedPaymentMethod: null,
        currentStep: 1,
        packageAmount: 0,
        isMonthly: true,

        // Initial non-persisted state
        isProcessing: false,
        error: null,

        // Actions
        setSelectedPlan: (plan) => {
            set({ selectedPlan: plan, error: null });
        },

        setPersonalInfo: (info) => {
            // console.log("setPersonalInfo", info);
            set({ personalInfo: info, error: null });
        },

        setIsMonthly: (isMonthly) => {
            set({ isMonthly });
        },

        updatePersonalInfo: (updates) => {
            const currentInfo = get().personalInfo;
            set({
                personalInfo: { ...currentInfo, ...updates },
                error: null,
            });
        },

        setSelectedPaymentMethod: (method) => {
            set({ selectedPaymentMethod: method, error: null });
        },

        setCurrentStep: (step) => {
            set({ currentStep: step });
        },

        setPackageAmount: (amount) => {
            set({ packageAmount: amount });
        },

        nextStep: () => {
            const currentStep = get().currentStep;
            if (currentStep < 4) {
                set({ currentStep: currentStep + 1 });
            }
        },

        previousStep: () => {
            const currentStep = get().currentStep;
            if (currentStep > 1) {
                set({ currentStep: currentStep - 1 });
            }
        },

        setProcessing: (processing) => {
            set({ isProcessing: processing });
        },

        setError: (error) => {
            set({ error });
        },

        resetSubscription: () => {
            set({
                selectedPlan: null,
                personalInfo: initialPersonalInfo,
                selectedPaymentMethod: null,
                currentStep: 1,
                isProcessing: false,
                error: null,
            });
        },

        canProceedToNextStep: () => {
            const { currentStep, selectedPlan, personalInfo, selectedPaymentMethod } = get();

            switch (currentStep) {
                case 1:
                    return selectedPlan !== null;
                case 2:
                    return (
                        personalInfo.name.trim() !== "" &&
                        personalInfo.mobile.trim() !== "" &&
                        personalInfo.email.trim() !== "" &&
                        personalInfo.hasAgreedToWaiver &&
                        personalInfo.isAbove18 &&
                        personalInfo.countryCode !== ""
                    );
                case 3:
                    return selectedPaymentMethod !== null;
                default:
                    return false;
            }
        },

        populateUserData: (userData) => {
            const currentInfo = get().personalInfo;
            const updates: Partial<PersonalInfo> = {};

            if (userData.name && !currentInfo.name) {
                updates.name = userData.name;
            }
            if (userData.email && !currentInfo.email) {
                updates.email = userData.email;
            }
            if (userData.phone && !currentInfo.mobile) {
                updates.mobile = userData.phone;
            }

            if (Object.keys(updates).length > 0) {
                set({
                    personalInfo: { ...currentInfo, ...updates },
                });
            }
        },
    }
        // ),
        // {
        //     name: "epic-padel-subscription-store", // unique name for localStorage key
        //     storage: createJSONStorage(() => localStorage),
        //     // Only persist the subscription data, not UI state
        //     partialize: (state: SubscriptionState) => ({
        //         selectedPlan: state.selectedPlan,
        //         personalInfo: state.personalInfo,
        //         selectedPaymentMethod: state.selectedPaymentMethod,
        //         currentStep: state.currentStep,
        //     }),
        // }
    )
);