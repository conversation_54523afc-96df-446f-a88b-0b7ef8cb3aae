export type CardBrand =
    | "visa"
    | "mastercard"
    | "amex"
    | "discover"
    | "diners"
    | "jcb"
    | "unionpay"
    | "maestro"
    | "mir"
    | "unknown"

export function cleanNumber(value: string): string {
    return value.replace(/\D/g, "")
}

export function detectCardBrand(digits: string): CardBrand {
    if (!digits) return "unknown"

    // Patterns based on IIN/BIN ranges commonly used
    if (/^4\d{0,18}$/.test(digits)) return "visa"
    if (/^(5[1-5]\d{0,14}|2(2[2-9]\d{0,12}|[3-6]\d{0,13}|7[01]\d{0,12}|720\d{0,12}))$/.test(digits)) return "mastercard"
    if (/^3[47]\d{0,13}$/.test(digits)) return "amex"
    if (
        /^(6011\d{0,12}|65\d{0,14}|64[4-9]\d{0,13}|622(12[6-9]\d{0,10}|1[3-9]\d{0,11}|[2-8]\d{0,12}|9[01]\d{0,11}|92[0-5]\d{0,10}))$/.test(
            digits,
        )
    )
        return "discover"
    if (/^3(0[0-5]\d{0,11}|[68]\d{0,12})$/.test(digits)) return "diners"
    if (/^(?:2131|1800|35\d{0,2})\d{0,12}$/.test(digits)) return "jcb"
    if (/^62\d{0,17}$/.test(digits)) return "unionpay"
    if (/^(?:50|5[6-9]|6\d)\d{0,16}$/.test(digits)) return "maestro"
    if (/^220[0-4]\d{0,12}$/.test(digits)) return "mir"
    return "unknown"
}

export function getMaxCardDigitsForBrand(brand: CardBrand): number {
    switch (brand) {
        case "amex":
            return 15
        case "diners":
            return 14
        case "mastercard":
        case "discover":
        case "mir":
            return 16
        case "visa":
        case "jcb":
        case "unionpay":
        case "maestro":
        case "unknown":
        default:
            return 19 // Visa can be up to 19, others vary; 19 is a safe upper bound
    }
}

export function formatCardNumber(digits: string, brand: CardBrand): string {
    if (!digits) return ""

    // Amex: 4-6-5
    if (brand === "amex") {
        const p1 = digits.slice(0, 4)
        const p2 = digits.slice(4, 10)
        const p3 = digits.slice(10, 15)
        return [p1, p2, p3].filter(Boolean).join(" ")
    }

    // Diners Club: 4-6-4
    if (brand === "diners") {
        const p1 = digits.slice(0, 4)
        const p2 = digits.slice(4, 10)
        const p3 = digits.slice(10, 14)
        return [p1, p2, p3].filter(Boolean).join(" ")
    }

    // Default: groups of 4
    const groups: string[] = []
    for (let i = 0; i < digits.length; i += 4) {
        groups.push(digits.slice(i, i + 4))
    }
    return groups.join(" ")
}

export function isValidLuhn(digits: string): boolean {
    if (!/^\d+$/.test(digits)) return false
    let sum = 0
    let shouldDouble = false
    for (let i = digits.length - 1; i >= 0; i--) {
        let d = Number.parseInt(digits.charAt(i), 10)
        if (shouldDouble) {
            d *= 2
            if (d > 9) d -= 9
        }
        sum += d
        shouldDouble = !shouldDouble
    }
    return sum % 10 === 0
}

export function isValidExpiry(input: string): boolean {
    // Accept "MM/YY" or "MM/YYYY"
    const match = input.match(/^(\d{2})\/(\d{2}|\d{4})$/)
    if (!match) return false
    const [, mmStr, yyStr] = match
    const month = Number.parseInt(mmStr, 10)
    if (month < 1 || month > 12) return false

    let year = Number.parseInt(yyStr, 10)
    if (yyStr.length === 2) {
        // Interpret 2-digit years in the current century pivoting around current year
        const nowYear = new Date().getFullYear() % 100
        const century = Math.floor(new Date().getFullYear() / 100) * 100
        const pivot = nowYear + 10 // next 10 years safe window
        if (year <= pivot) {
            year = century + year
        } else {
            year = century - 100 + year // assume previous century rollover if far in future
        }
    }
    // Expiry is end of month; consider valid through that month
    const expiryEnd = new Date(year, month, 0, 23, 59, 59, 999) // day 0 of next month = last day of month
    const now = new Date()
    // Compare at end-of-day precision
    return expiryEnd.getTime() >= now.getTime()
}

export function getCvvLengthForBrand(brand: CardBrand): number {
    return brand === "amex" ? 4 : 3
}

export function isValidCvv(cvv: string, brand: CardBrand): boolean {
    if (!/^\d+$/.test(cvv)) return false
    const len = getCvvLengthForBrand(brand)
    // For unknown brands, accept 3 or 4 for flexibility
    if (brand === "unknown") return cvv.length === 3 || cvv.length === 4
    return cvv.length === len
}