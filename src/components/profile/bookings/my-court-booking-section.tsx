import { useGetMyCourtBookingList } from "@/api/my-booking-service";
import { Skeleton } from "@/components/ui/skeleton";
import { useBookingStore } from "@/store/booking-store";
import { useRouter } from "next/navigation";
import React, { useCallback } from "react";
import MyCourtBookingListItem from "./my-court-booking-list-item";
import { useCheckoutStore } from "@/store/checkout-store";

interface CourtBookingSectionProps {
  timeFilter: "upcoming" | "past";
}

const MyCourtBookingSection: React.FC<CourtBookingSectionProps> = ({ timeFilter }) => {
  const router = useRouter();

  const {
    myCourtBookingList,
    myCourtBookingListLoading,
    myCourtBookingListError,
    myCourtBookingListEmpty,
  } = useGetMyCourtBookingList({ page: 1, limit: 25, type: timeFilter });

  const { setCourtBookingDetails } = useBookingStore();
  const { updateCourtAttribute } = useCheckoutStore();

  // Handle view details
  const handleViewDetails = useCallback((booking: any) => {
    // TODO: Navigate to booking details page or open modal
    console.log("View details for booking:", booking);
  }, []);

  const handleEditCourtBooking = useCallback((booking: any) => {
    console.log("Edit booking:", booking);
    const startTime = new Date(booking?.start_time);
    const endTime = new Date(booking?.end_time);
    const date = startTime.toISOString().split("T")[0];
    const hours = String(startTime.getUTCHours()).padStart(2, "0");
    const minutes = String(startTime.getUTCMinutes()).padStart(2, "0");
    const time = `${hours}:${minutes}`;

    const attributes =
      booking?.bookingAttributes?.map((attr: any) => {
        const ca = {
          court_attribute_id: attr.court_attribute_id,
          value: attr.value,
        };
        updateCourtAttribute(attr.court_attribute_id, attr.value);
        return ca;
      }) || [];

    const bookingData = {
      booking_id: booking.id,
      court_id: booking?.court?.id,
      sub_court_id: booking?.sub_court?.id,
      start_time: startTime,
      end_time: endTime,
      court_name: booking?.court?.name,
      sub_court_name: booking?.sub_court?.name,
      date: date,
      time: time,
      duration: `${booking?.duration} mins`,
      is_peak_time: booking.is_peak_hour ? true : false,
      split: booking.last_time_to_pay ? true : false,
      attributes: attributes,
    };

    setCourtBookingDetails(bookingData);
    router.push("/booking?booking_type=court&edit=true");
  }, []);

  const handleCancelBooking = useCallback((booking: any) => {
    // TODO: Cancel booking
    console.log("Cancel booking:", booking);
  }, []);

  // Loading skeleton
  if (myCourtBookingListLoading) {
    return (
      <div className="space-y-4">
        <div className="mb-4 text-sm text-gray-600">Loading {timeFilter} bookings...</div>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-3 rounded-2xl border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-6 w-6 rounded" />
            </div>
            <Skeleton className="h-px w-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-1/3" />
            </div>
            <div className="flex items-center justify-between pt-2">
              <Skeleton className="h-6 w-24 rounded-full" />
              <Skeleton className="h-8 w-28 rounded" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (myCourtBookingListError) {
    return (
      <div className="py-8 text-center">
        <div className="mb-2 text-red-600">Error loading bookings</div>
        <div className="text-sm text-gray-500">
          {myCourtBookingListError.message || "Something went wrong"}
        </div>
      </div>
    );
  }

  // Empty state
  if (myCourtBookingListEmpty) {
    return (
      <div className="py-8 text-center">
        <div className="mb-2 text-gray-600">No {timeFilter} bookings found</div>
        <div className="text-sm text-gray-500">
          {timeFilter === "upcoming"
            ? "You don't have any upcoming court bookings."
            : "You don't have any past court bookings."}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <div className="mb-4 text-sm text-gray-600">
        Showing {myCourtBookingList.length} {timeFilter} booking
        {myCourtBookingList.length !== 1 ? "s" : ""}
      </div>

      {/* Debug info - remove in production */}
      {/* {process.env.NODE_ENV === "development" && (
        <details className="mb-4">
          <summary className="cursor-pointer text-xs text-gray-400">Debug: Raw API Data</summary>
          <pre className="mt-2 overflow-auto rounded bg-gray-100 p-2 text-xs">
            {JSON.stringify(myCourtBookingList, null, 2)}
          </pre>
        </details>
      )} */}

      <div className="flex max-h-[400px] w-full flex-col gap-2 overflow-y-auto p-4">
        {myCourtBookingList.map((booking: any, index: number) => (
          <MyCourtBookingListItem
            key={booking.id || index}
            booking={booking}
            onViewDetails={() => handleViewDetails(booking)}
            onEditCourtBooking={() => handleEditCourtBooking(booking)}
            onCancelBooking={() => handleCancelBooking(booking)}
            timeFilter={timeFilter}
          />
        ))}
      </div>
    </div>
  );
};

export default MyCourtBookingSection;
