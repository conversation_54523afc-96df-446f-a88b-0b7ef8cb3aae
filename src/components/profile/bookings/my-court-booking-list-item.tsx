import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/libs/utils";
import { CourtBookingItem } from "@/types/booking";
import { MoreHorizontal, PencilIcon, X } from "lucide-react";
import React, { useMemo } from "react";

interface MyCourtBookingListItemProps {
  booking?: any;
  className?: string;
  timeFilter: "upcoming" | "past";
  onViewDetails?: () => void;
  onEditCourtBooking?: () => void;
  onCancelBooking?: () => void;
}

const MyCourtBookingListItem: React.FC<MyCourtBookingListItemProps> = ({
  booking,
  className,
  timeFilter,
  onViewDetails,
  onEditCourtBooking,
  onCancelBooking,
}) => {
  const bookingData = booking;

  // Format date and time
  const formattedDate = useMemo(() => {
    if (!bookingData.start_time) return "Date not available";

    try {
      const date = new Date(bookingData.start_time);
      // console.log("formatted date", date);
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        day: "numeric",
        month: "short",
        year: "numeric",
      });
    } catch {
      return "Invalid date";
    }
  }, [bookingData.date]);

  const formattedTime = useMemo(() => {
    if (!bookingData.start_time || !bookingData.end_time) return "Time not available";

    try {
      const formatTime = (time: string) => {
        const date = new Date(time);
        let hours: any = date.getUTCHours();
        const minutes = String(date.getUTCMinutes()).padStart(2, "0");
        const ampm = hours >= 12 ? "PM" : "AM";

        hours = hours % 12 || 12;
        hours = String(hours).padStart(2, "0");

        const formattedTime = `${hours}.${minutes} ${ampm}`;
        return formattedTime;
      };

      return `${formatTime(bookingData.start_time)} - ${formatTime(bookingData.end_time)}`;
    } catch {
      return `${bookingData.start_time} - ${bookingData.end_time}`;
    }
  }, [bookingData.start_time, bookingData.end_time]);

  // Status styling
  const statusConfig: any = {
    confirmed: {
      bg: "bg-[#f8ffd6]",
      border: "outline-[#c0e702]",
      text: "text-[#1c5534]",
    },
    pending: {
      bg: "bg-yellow-50",
      border: "outline-yellow-400",
      text: "text-yellow-700",
    },
    cancelled: {
      bg: "bg-red-50",
      border: "outline-red-400",
      text: "text-red-700",
    },
    completed: {
      bg: "bg-gray-50",
      border: "outline-gray-400",
      text: "text-gray-700",
    },
  };

  const currentStatus = statusConfig[bookingData.status || "confirmed"];

  return (
    <div
      className={cn(
        "relative w-full rounded-2xl border border-gray-200 bg-white p-4 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",
        "focus-within:ring-opacity-20 focus-within:ring-primary focus-within:ring-2",
        className
      )}
      onClick={() => onViewDetails?.()}
    >
      {/* Header with booking ID and menu */}
      <div className="mb-1 flex items-center justify-between">
        <div className="flex items-center gap-1 text-sm">
          <span className="text-gray-400">Booking</span>
          <span className="font-medium text-gray-900">#{bookingData?.id}</span>
        </div>

        {/* {timeFilter === "upcoming" && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100"
                aria-label="Booking actions"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="inline-flex w-44 flex-col items-start justify-start gap-1 rounded-[15px] border border-gray-400 bg-white px-[9px] py-2.5 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.10)] outline-1 outline-[#e2e2e2]"
            >
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onEditCourtBooking?.();
                }}
              >
                <PencilIcon className="mr-2 h-4 w-4" />
                Edit Booking
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onCancelBooking?.();
                }}
              >
                <X className="mr-2 h-4 w-4 text-red-500" />
                Cancel Booking
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )} */}
      </div>

      {/* Divider */}
      <div className="mb-2 h-px w-full border-1 border-dashed border-gray-200 bg-gradient-to-r from-transparent via-gray-200 to-transparent px-12" />

      {/* Main content */}
      <div className="space-y-1">
        {/* Court name and details */}
        <div className="space-y-1">
          <h3 className="text-lg leading-tight font-medium text-gray-900">
            {bookingData?.court?.name || "Court name not available"}
          </h3>
          <p className="text-sm text-gray-600">{formattedDate}</p>
          <p className="text-sm text-gray-600">{formattedTime}</p>
        </div>

        {/* Bottom section with status and action button */}
        <div className="flex items-center justify-between pt-2">
          {/* Status badge */}
          <div
            className={cn(
              "inline-flex items-center rounded-full px-3 py-1 text-sm font-normal outline-1 outline-offset-[-1px]",
              currentStatus.bg,
              currentStatus.border,
              currentStatus.text
            )}
          >
            {bookingData.booking_type || "Court Booking"}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyCourtBookingListItem;
