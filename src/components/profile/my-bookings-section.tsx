"use client";

import React, { useCallback, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import CourtBookingSection from "./bookings/my-court-booking-section";

interface MyBookingsSectionProps {
  activeSection: string;
}

const MyBookingsSection: React.FC<MyBookingsSectionProps> = ({ activeSection }) => {
  const [timeFilter, setTimeFilter] = useState<"upcoming" | "past">("upcoming");
  const [activeTab, setActiveTab] = useState<"court" | "lesson" | "program">("court");

  const handleBookingTimeSelector = useCallback(
    (timeFilter: "upcoming" | "past") => {
      setTimeFilter(timeFilter);
      console.log("Booking time filter changed to", timeFilter);
    },
    [setTimeFilter]
  );

  const handleTabChange = (value: string) => {
    setActiveTab(value as "court" | "lesson" | "program");
  };

  const getActiveTabWidth = () => {
    switch (activeTab) {
      case "court":
        return "w-[36px]";
      case "lesson":
        return "w-[42px]";
      case "program":
        return "w-[52px]";
      default:
        return "w-[36px]";
    }
  };

  const getActiveTabPosition = () => {
    switch (activeTab) {
      case "court":
        return "left-0";
      case "lesson":
        return "left-[90px]";
      case "program":
        return "left-[190px]";
      default:
        return "left-0";
    }
  };

  return (
    <div
      className={`w-full flex-1 bg-white transition-all duration-300 ${
        activeSection === "bookings-section" ? "p-4 sm:p-6 lg:p-8" : "hidden"
      }`}
      id="bookings-section"
    >
      {/* Header Section */}
      <div className="mb-8">
        <h2 className="mb-2 text-xl font-bold text-gray-900 sm:text-2xl">My Bookings</h2>
        <p className="text-sm text-gray-600 sm:text-base">
          Manage your court reservations, lessons, and program enrollments
        </p>
      </div>

      <Tabs defaultValue="court" className="flex w-full flex-col" onValueChange={handleTabChange}>
        {/* Custom Tab Navigation */}
        <div className="mb-4">
          <div className="relative flex flex-col gap-2">
            <TabsList className="flex h-auto items-center justify-start gap-8 bg-transparent p-0 sm:gap-12">
              <TabsTrigger
                value="court"
                className="relative border-none bg-transparent p-0 text-sm font-medium text-gray-500 shadow-none transition-all duration-200 hover:text-gray-700 data-[state=active]:bg-transparent data-[state=active]:font-semibold data-[state=active]:text-[#1c5534] data-[state=active]:shadow-none sm:text-base"
              >
                Court
              </TabsTrigger>
              {/* <TabsTrigger
                value="lesson"
                className="relative border-none bg-transparent p-0 text-sm font-medium text-gray-500 shadow-none transition-all duration-200 hover:text-gray-700 data-[state=active]:bg-transparent data-[state=active]:font-semibold data-[state=active]:text-[#1c5534] data-[state=active]:shadow-none sm:text-base"
              >
                Lesson
              </TabsTrigger>
              <TabsTrigger
                value="program"
                className="relative border-none bg-transparent p-0 text-sm font-medium text-gray-500 shadow-none transition-all duration-200 hover:text-gray-700 data-[state=active]:bg-transparent data-[state=active]:font-semibold data-[state=active]:text-[#1c5534] data-[state=active]:shadow-none sm:text-base"
              >
                Program
              </TabsTrigger> */}
            </TabsList>

            {/* Tab Underlines */}
            <div className="relative">
              <div className="h-0.5 w-full rounded-full bg-gray-200"></div>
              <div
                className={`absolute top-0 h-0.5 rounded-full bg-[#1c5534] transition-all duration-300 ease-in-out ${getActiveTabWidth()} ${getActiveTabPosition()}`}
              ></div>
            </div>
          </div>
        </div>

        {/* Time Filter Section */}
        <div className="mb-4 flex justify-start sm:justify-start">
          <div className="inline-flex items-center rounded-full border border-gray-100 bg-gray-50 p-1 shadow-sm">
            <button
              onClick={() => handleBookingTimeSelector("upcoming")}
              className={`relative rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 sm:px-6 ${
                timeFilter === "upcoming"
                  ? "border border-gray-200 bg-white text-[#1c5534]"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-800"
              }`}
            >
              <span className="relative z-10">Upcoming</span>
              {timeFilter === "upcoming" && (
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#1c5534]/5 to-[#1c5534]/10"></div>
              )}
            </button>
            <button
              onClick={() => handleBookingTimeSelector("past")}
              className={`relative rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 sm:px-6 ${
                timeFilter === "past"
                  ? "border border-gray-200 bg-white text-[#1c5534]"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-800"
              }`}
            >
              <span className="relative z-10">Past</span>
              {timeFilter === "past" && (
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#1c5534]/5 to-[#1c5534]/10"></div>
              )}
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          <TabsContent value="court" className="mt-0 w-full focus:outline-none">
            <div className="">
              <CourtBookingSection timeFilter={timeFilter} />
            </div>
          </TabsContent>

          <TabsContent value="lesson" className="mt-0 w-full focus:outline-none">
            <div className="rounded-xl border border-gray-100 bg-gray-50/50 p-4 sm:p-6">
              <div className="py-12 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[#1c5534]/10">
                  <svg
                    className="h-8 w-8 text-[#1c5534]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  {timeFilter === "upcoming" ? "No Upcoming Lessons" : "No Past Lessons"}
                </h3>
                <p className="mb-4 text-gray-600">
                  {timeFilter === "upcoming"
                    ? "You don't have any upcoming lesson bookings."
                    : "You haven't taken any lessons yet."}
                </p>
                {timeFilter === "upcoming" && (
                  <button className="rounded-lg bg-[#1c5534] px-6 py-2 text-white transition-colors duration-200 hover:bg-[#1c5534]/90">
                    Book a Lesson
                  </button>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="program" className="mt-0 w-full focus:outline-none">
            <div className="rounded-xl border border-gray-100 bg-gray-50/50 p-4 sm:p-6">
              <div className="py-12 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[#1c5534]/10">
                  <svg
                    className="h-8 w-8 text-[#1c5534]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  {timeFilter === "upcoming" ? "No Upcoming Programs" : "No Past Programs"}
                </h3>
                <p className="mb-4 text-gray-600">
                  {timeFilter === "upcoming"
                    ? "You're not enrolled in any upcoming programs."
                    : "You haven't participated in any programs yet."}
                </p>
                {timeFilter === "upcoming" && (
                  <button className="rounded-lg bg-[#1c5534] px-6 py-2 text-white transition-colors duration-200 hover:bg-[#1c5534]/90">
                    Browse Programs
                  </button>
                )}
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default MyBookingsSection;
