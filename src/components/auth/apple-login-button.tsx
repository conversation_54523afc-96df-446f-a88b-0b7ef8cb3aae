"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";

// Apple Sign-In configuration
const APPLE_CONFIG = {
  clientId: process.env.NEXT_PUBLIC_APPLE_NEW_CLIENT_ID!,
  scope: "name email",
  redirectURI: typeof window !== "undefined" ? window.location.origin : "",
  state: "signin",
  usePopup: true,
};

// Apple Sign-In response interface
interface AppleSignInResponse {
  authorization: {
    code: string;
    id_token: string;
    state: string;
  };
  user?: {
    email: string;
    name: {
      firstName: string;
      lastName: string;
    };
  };
}

const AppleLoginButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isAppleLoaded, setIsAppleLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    // Add debug info
    const debug = `
      Client ID: ${APPLE_CONFIG.clientId}
      Redirect URI: ${APPLE_CONFIG.redirectURI}
      Current URL: ${typeof window !== "undefined" ? window.location.href : "N/A"}
    `;
    setDebugInfo(debug);
    console.log("Apple Sign-In Debug Info:", debug);

    // Validate configuration
    if (!APPLE_CONFIG.clientId) {
      setError("Apple Client ID is not configured");
      console.error("NEXT_PUBLIC_APPLE_NEW_CLIENT_ID is missing");
      return;
    }

    // Load Apple Sign-In JavaScript SDK
    const loadAppleScript = () => {
      if (document.getElementById("apple-signin-script")) {
        console.log("Apple script already loaded");
        setIsAppleLoaded(true);
        initializeAppleSignIn();
        return;
      }

      console.log("Loading Apple Sign-In script...");
      const script = document.createElement("script");
      script.id = "apple-signin-script";
      script.src =
        "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js";
      script.async = true;
      script.onload = () => {
        console.log("Apple Sign-In script loaded successfully");
        setIsAppleLoaded(true);
        initializeAppleSignIn();
      };
      script.onerror = (err) => {
        console.error("Failed to load Apple Sign-In script:", err);
        setError("Failed to load Apple Sign-In. Please check your internet connection.");
      };
      document.head.appendChild(script);
    };

    loadAppleScript();
  }, []);

  const initializeAppleSignIn = () => {
    try {
      if (typeof window !== "undefined" && (window as any).AppleID) {
        console.log("Initializing Apple Sign-In with config:", APPLE_CONFIG);

        (window as any).AppleID.auth.init({
          clientId: APPLE_CONFIG.clientId,
          scope: APPLE_CONFIG.scope,
          redirectURI: APPLE_CONFIG.redirectURI,
          state: APPLE_CONFIG.state,
          usePopup: APPLE_CONFIG.usePopup,
        });

        console.log("Apple Sign-In initialized successfully");
        setError(null);
      } else {
        console.error("AppleID object not found on window");
        setError("Apple Sign-In SDK not available");
      }
    } catch (err) {
      console.error("Error initializing Apple Sign-In:", err);
      setError("Failed to initialize Apple Sign-In");
    }
  };

  const handleAppleSignIn = async () => {
    console.log("Apple Sign-In button clicked");

    if (!isAppleLoaded) {
      setError("Apple Sign-In is not loaded yet. Please wait and try again.");
      return;
    }

    if (!(window as any).AppleID) {
      setError("Apple Sign-In SDK not available");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Starting Apple Sign-In flow...");

      // Check if popups are blocked
      const testPopup = window.open("", "_blank", "width=1,height=1");
      if (!testPopup || testPopup.closed) {
        throw new Error("Popups are blocked. Please enable popups for this site.");
      }
      testPopup.close();

      const response: AppleSignInResponse = await (window as any).AppleID.auth.signIn();

      console.log("Apple Sign-In Response received:", {
        hasAuthorization: !!response.authorization,
        hasUser: !!response.user,
        authCode: response.authorization?.code ? "present" : "missing",
        idToken: response.authorization?.id_token ? "present" : "missing",
      });

      // Extract user information
      const { authorization, user } = response;

      if (!authorization || !authorization.id_token) {
        throw new Error("No authorization data received from Apple");
      }

      // Decode the ID token to get user information
      const userInfo = decodeJWT(authorization.id_token);

      console.log("User Info from JWT:", userInfo);
      console.log("User Info from response:", user);

      // Handle successful sign-in
      await handleSuccessfulSignIn({
        idToken: authorization.id_token,
        authorizationCode: authorization.code,
        userInfo,
        user,
      });
    } catch (error: any) {
      console.error("Apple Sign-In Error Details:", {
        error,
        errorType: typeof error,
        errorMessage: error?.message || error,
        errorStack: error?.stack,
      });

      let errorMessage = "Apple Sign-In failed. Please try again.";

      // Handle different error types
      if (typeof error === "string") {
        switch (error) {
          case "popup_closed_by_user":
            errorMessage = "Sign-in was cancelled. Please try again.";
            break;
          case "user_cancelled_authorize":
            errorMessage = "Authorization was cancelled.";
            break;
          case "popup_blocked_by_browser":
            errorMessage = "Popups are blocked. Please enable popups and try again.";
            break;
          default:
            errorMessage = `Apple Sign-In error: ${error}`;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const decodeJWT = (token: string) => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return null;
    }
  };

  const handleSuccessfulSignIn = async (data: {
    idToken: string;
    authorizationCode: string;
    userInfo: any;
    user?: any;
  }) => {
    try {
      const { idToken, authorizationCode, userInfo, user } = data;

      // Extract user details
      const email = userInfo?.email || user?.email;
      const firstName = user?.name?.firstName || userInfo?.given_name || "";
      const lastName = user?.name?.lastName || userInfo?.family_name || "";
      const fullName = `${firstName} ${lastName}`.trim();

      console.log("Processing sign-in for:", {
        email,
        name: fullName,
        sub: userInfo?.sub, // Apple's unique user identifier
      });

      // Send to your backend API
      console.log("Sending data to backend...");
      const response = await fetch("/api/auth/apple", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          idToken,
          authorizationCode,
          email,
          name: fullName,
          appleUserId: userInfo?.sub,
        }),
      });

      console.log("Backend response status:", response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error("Backend error response:", errorData);
        throw new Error(`Backend authentication failed: ${response.status}`);
      }

      const result = await response.json();
      console.log("Backend response:", result);

      if (result.success) {
        console.log("Authentication successful, redirecting...");
        // Handle successful authentication
        window.location.href = "/dashboard";
      } else {
        throw new Error(result.error || "Authentication failed");
      }
    } catch (error: any) {
      console.error("Error processing sign-in:", error);
      setError(`Sign-in processing failed: ${error.message}`);
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={handleAppleSignIn}
        disabled={!isAppleLoaded || isLoading}
        className="flex w-full cursor-pointer items-center justify-center space-x-3 rounded-full bg-black px-4 py-3 text-white transition-colors hover:bg-gray-800 disabled:cursor-not-allowed disabled:opacity-50"
      >
        {isLoading ? (
          <>
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
            <span>Signing in...</span>
          </>
        ) : (
          <>
            <Image
              src="/imgs/apple.svg"
              alt="Apple"
              width={20}
              height={20}
              title="Apple"
              className="h-5 w-5"
              loading="lazy"
            />
            <span>Sign in with Apple</span>
          </>
        )}
      </button>

      {/* Debug Info (only in development) */}
      {process.env.NODE_ENV === "development" && (
        <details className="mt-4">
          <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
            Debug Information
          </summary>
          <div className="mt-2 rounded border bg-gray-50 p-3 font-mono text-xs whitespace-pre-wrap">
            <div>
              <strong>Apple SDK Loaded:</strong> {isAppleLoaded ? "Yes" : "No"}
            </div>
            <div>
              <strong>Configuration:</strong>
              {debugInfo}
            </div>
            <div>
              <strong>Window.AppleID:</strong>{" "}
              {typeof window !== "undefined" && (window as any).AppleID
                ? "Available"
                : "Not Available"}
            </div>
          </div>
        </details>
      )}
    </div>
  );
};

export default AppleLoginButton;
