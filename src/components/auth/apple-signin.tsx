"use client";

import { appleLoginV2 } from "@/api/auth-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import { useUIStore } from "@/store/ui-store";
import Image from "next/image";
import { useState } from "react";
import AppleSignin from "react-apple-signin-auth";
import Loader from "../loader";

interface AppleAuthResponse {
  authorization: {
    code: string;
    id_token: string;
    state?: string;
  };
  user?: {
    email: string;
    name: {
      firstName: string;
      lastName: string;
    };
  };
}

interface AppleSignInProps {
  resetForm: () => void;
  checkForStoredPlan: () => void;
}

export default function AppleSignIn({ resetForm, checkForStoredPlan }: AppleSignInProps) {
  const { isLoginDialogOpen, openLoginDialog, closeLoginDialog, openMembershipDialog } =
    useUIStore();
  const { trackCompleteRegistration } = useFacebookPixel();
  const [loading, setLoading] = useState(false);

  const handleAppleSuccess = async (response: AppleAuthResponse) => {
    try {
      console.log("Apple Sign-In Success:", response);

      // Extract user information
      const userInfo = {
        email: response.user?.email,
        name: response.user?.name
          ? `${response.user.name.firstName} ${response.user.name.lastName}`
          : null,
        idToken: response.authorization.id_token,
        authCode: response.authorization.code,
      };

      const apiResponse = await appleLoginV2(userInfo);

      console.log("apiResponse", apiResponse);

      closeLoginDialog();
      resetForm();
      checkForStoredPlan();
      if (apiResponse?.registration) {
        trackCompleteRegistration();
      }
      // Here you would typically send the authorization code and id_token to your backend
      // to verify the user and create a session
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAppleError = (error: any) => {
    console.error("Apple Sign-In Error:", error);
    setLoading(false);
  };

  return (
    <AppleSignin
      authOptions={{
        clientId: process.env.NEXT_PUBLIC_APPLE_NEW_CLIENT_ID || "com.epic-padel.web",
        scope: "name email",
        redirectURI: typeof window !== "undefined" ? window.location.origin : "",
        state: "signin",
        nonce: "nonce-" + Math.random().toString(36).substring(2, 15),
        usePopup: true,
      }}
      uiType="dark"
      onSuccess={handleAppleSuccess}
      onError={handleAppleError}
      skipScript={false}
      render={(props: any) => (
        <button
          {...props}
          className="flex w-full cursor-pointer items-center justify-center space-x-3 rounded-full bg-black px-4 py-3 text-white transition-colors hover:bg-gray-800"
          disabled={loading}
          onClick={() => {
            setLoading(true);
            props.onClick();
          }}
        >
          <Image
            src="/imgs/apple.svg"
            alt="Apple"
            width={20}
            height={20}
            title="Apple"
            className="h-5 w-5"
            loading="lazy"
          />
          <span>{loading ? <Loader /> : "Sign in with Apple"}</span>
        </button>
      )}
    />
  );
}
