import React, { useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import BookingCourtSection from "./court/booking-court-section";
import BookingProgramSection from "./programs/booking-program-section";
import BookingLessonSection from "./lessons/booking-lesson-section";
import BookingInstructorSection from "./instructors/booking-instructor-section";
import { useRouter, useSearchParams } from "next/navigation";
import { LocationSelector } from "./location-selector";
import { useBookingStore } from "@/store/booking-store";
import BookingCourtSectionEdit from "./court/booking-court-section-edit";

const BookingTypeSelector = ({
  locationId,
  bookingType,
  isFromBookingPage = false,
  isEdit = false,
}: {
  locationId: number;
  bookingType?: string;
  isFromBookingPage?: boolean;
  isEdit?: boolean;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { clearAllBookings } = useBookingStore();

  const handleTabChange = useCallback(
    (value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set("booking_type", value);
      router.push(`?${params.toString()}`, { scroll: false });
      clearAllBookings();
    },
    [router, searchParams]
  );

  return (
    <Tabs
      defaultValue={bookingType || "court"}
      onValueChange={handleTabChange}
      className="container w-full"
    >
      <TabsList className="h-[68px] w-full rounded-[100px] bg-neutral-100 p-1">
        <TabsTrigger
          value="court"
          className="font-helvetica h-full flex-1 rounded-[30px] text-xs font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696] md:text-base"
        >
          Book a Court
        </TabsTrigger>
        <TabsTrigger
          value="program"
          className="font-helvetica h-full flex-1 rounded-[30px] text-xs font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696] md:text-base"
        >
          Programs
        </TabsTrigger>
        <TabsTrigger
          value="lesson"
          className="font-helvetica h-full flex-1 rounded-[30px] text-xs font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696] md:text-base"
        >
          Lessons
        </TabsTrigger>
        <TabsTrigger
          value="instructor"
          className="font-helvetica h-full flex-1 rounded-[30px] text-xs font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696] md:text-base"
        >
          Instructors
        </TabsTrigger>
      </TabsList>

      {isFromBookingPage && <LocationSelector />}

      <TabsContent value="court" className="mt-6">
        {isEdit ? (
          <BookingCourtSectionEdit locationId={locationId} />
        ) : (
          <BookingCourtSection locationId={locationId} />
        )}
      </TabsContent>

      <TabsContent value="program" className="mt-6">
        <BookingProgramSection locationId={locationId} />
      </TabsContent>

      <TabsContent value="lesson" className="mt-6">
        <BookingLessonSection locationId={locationId} />
      </TabsContent>

      <TabsContent value="instructor" className="mt-6">
        <BookingInstructorSection locationId={locationId} />
      </TabsContent>
    </Tabs>
  );
};

export default BookingTypeSelector;
