import {
  useGetPublicCourtById,
  useGetPublicCourtList,
  useGetPublicNextOpeningNew,
} from "@/api/booking-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChevronDown, ChevronDownIcon } from "lucide-react";
import React, { useCallback, useMemo, useState, useEffect, use } from "react";
import { Button } from "../../ui/button";
import { Calendar } from "../../ui/calendar";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { extractErrorMessage } from "@/libs/utils";
import Loader from "../../loader";
import { useBookingStore } from "@/store/booking-store";
import { Court } from "@/types/booking";
import { useBookingEstimate, useValidateRedeem } from "@/hooks/use-booking-estimate";
import { Skeleton } from "@/components/ui/skeleton";

interface SelectedDetails {
  subCourtId: number;
  bookingPriceId: number;
  date: string;
}

const BookingCourtSection = ({ locationId }: { locationId: number }) => {
  const router = useRouter();
  const [isBooking, setIsBooking] = useState(false);
  const { setCourtBookingDetails, courtBookingDetails } = useBookingStore();

  const [open, setOpen] = React.useState(false);
  const [date, setDate] = React.useState<Date | undefined>(
    courtBookingDetails?.date ? new Date(`${courtBookingDetails.date}T00:00:00`) : undefined
  );
  const [selectedDetails, setSelectedDetails] = useState<SelectedDetails>({
    subCourtId: courtBookingDetails?.sub_court_id || 0,
    bookingPriceId: courtBookingDetails?.booking_price_id || 0,
    date: courtBookingDetails?.date || "",
  });

  // Update local state when courtBookingDetails changes
  useEffect(() => {
    if (courtBookingDetails) {
      if (courtBookingDetails.date) {
        setDate(new Date(`${courtBookingDetails.date}T00:00:00`));
      }

      setSelectedDetails({
        subCourtId: courtBookingDetails.sub_court_id || 0,
        bookingPriceId: courtBookingDetails.booking_price_id || 0,
        date: courtBookingDetails.date || "",
      });
    }
  }, [courtBookingDetails]);

  const { publicCourtList, publicCourtListLoading, publicCourtListError } = useGetPublicCourtList({
    page: 1,
    limit: 10,
    courtLocationId: locationId,
  });

  const firstCourtId = useMemo(() => {
    return publicCourtList && publicCourtList.length > 0 ? publicCourtList[0].id : null;
  }, [publicCourtList]);

  const { publicCourtDetails, publicCourtDetailsLoading, publicCourtDetailsError } =
    useGetPublicCourtById({
      courtId: firstCourtId,
    });

  const { publicNextOpeningNew, publicNextOpeningNewLoading, publicNextOpeningNewError } =
    useGetPublicNextOpeningNew({
      courtId: firstCourtId,
      date: selectedDetails?.date,
      bookingPriceId: selectedDetails?.bookingPriceId,
      subCourtId: selectedDetails?.subCourtId,
    });

  // Debug logs to see the synchronization
  // console.log("publicCourtList", publicCourtList);
  // console.log("firstCourtId", firstCourtId);
  // console.log("publicCourtDetails", publicCourtDetails);
  // console.log("Loading states:", {
  //   courtListLoading: publicCourtListLoading,
  //   courtDetailsLoading: publicCourtDetailsLoading,
  // });

  // console.log("selectedDetails", selectedDetails);
  // console.log("publicNextOpeningNew", publicNextOpeningNew);

  // Optimized unified handler for updating booking data and selected details
  const updateBookingDetails = useCallback(
    (updates: {
      // Display fields
      court?: string;
      date?: string;
      time?: string;
      duration?: string;
      // Internal fields
      subCourtId?: number;
      bookingPriceId?: number;
      price?: number;
      redeem?: boolean;
    }) => {
      // Update local selected details state
      setSelectedDetails((prev) => ({
        ...prev,
        ...(updates.subCourtId !== undefined && { subCourtId: updates.subCourtId }),
        ...(updates.bookingPriceId !== undefined && { bookingPriceId: updates.bookingPriceId }),
        ...(updates.date !== undefined && { date: updates.date }),
      }));

      // Create updated booking details for store
      const updatedDetails = courtBookingDetails
        ? { ...courtBookingDetails }
        : {
            court_id: 0,
            sub_court_id: 0,
            sub_court_name: "",
            booking_price_id: 0,
            start_time: "",
            end_time: "",
            split: false,
            date: "",
            time: "",
            duration: "",
            price: 0,
            is_peak_time: false,
            redeem: false,
          };

      // Apply all updates atomically
      if (updates.court !== undefined) {
        updatedDetails.sub_court_name = updates.court;
      }
      if (updates.date !== undefined) {
        updatedDetails.date = updates.date;
      }
      if (updates.time !== undefined) {
        updatedDetails.time = updates.time;
      }
      if (updates.duration !== undefined) {
        updatedDetails.duration = updates.duration;
      }
      if (updates.subCourtId !== undefined) {
        updatedDetails.sub_court_id = updates.subCourtId;
      }
      if (updates.bookingPriceId !== undefined) {
        updatedDetails.booking_price_id = updates.bookingPriceId;
      }
      if (updates.price !== undefined) {
        updatedDetails.price = updates.price;
      }
      if (updates.redeem !== undefined) {
        updatedDetails.redeem = updates.redeem;
      }

      setCourtBookingDetails(updatedDetails);
    },
    [courtBookingDetails, setCourtBookingDetails]
  );

  //   console.log("bookingData", bookingData);

  // Memoized clutch AI status text
  //   const clutchAiStatus = useMemo(() => {
  //     return bookingData.clutchAi ? "On" : "Off";
  //   }, [bookingData.clutchAi]);

  // Handler for clutch AI toggle
  //   const handleClutchAiChange = useCallback(
  //     (enabled: boolean) => {
  //       updateBookingData("clutchAi", enabled);
  //     },
  //     [updateBookingData]
  //   );

  // Memoized pricing calculations
  const pricing = useMemo(() => {
    let courtBooking = 0.0;

    // Find the selected booking price details
    const selectedBookingPrice = publicCourtDetails?.booking_prices?.find(
      (bp: any) => bp.id === selectedDetails.bookingPriceId
    );

    // Check if the selected time slot is peak
    const selectedTimeSlot = publicNextOpeningNew?.all_slots?.find(
      (slot: any) => slot.start === courtBookingDetails?.time
    );
    const isPeakTime = selectedTimeSlot?.peak || false;

    // Use peak_price if it's peak time, otherwise use regular price
    if (selectedBookingPrice) {
      courtBooking = parseFloat(
        isPeakTime ? selectedBookingPrice.peak_price : selectedBookingPrice.price
      );
    }

    return {
      courtBooking,
      isPeakTime,
    };
  }, [
    publicCourtDetails?.booking_prices,
    selectedDetails.bookingPriceId,
    publicNextOpeningNew?.all_slots,
    courtBookingDetails?.time,
  ]);

  // Calculate start_time and end_time in 24-hour format
  const bookingTimes = useMemo(() => {
    if (
      !courtBookingDetails?.date ||
      !courtBookingDetails?.time ||
      !courtBookingDetails?.duration
    ) {
      return {
        start_time: "",
        end_time: "",
      };
    }

    // Parse the duration to get minutes (e.g., "150 mins" -> 150)
    const durationMatch = courtBookingDetails.duration.match(/(\d+)\s*mins?/);
    const durationMinutes = durationMatch ? parseInt(durationMatch[1], 10) : 0;

    // Create start time: combine date and time
    const startDateTime = new Date(`${courtBookingDetails.date}T${courtBookingDetails.time}:00`);

    // Calculate end time by adding duration
    const endDateTime = new Date(startDateTime.getTime() + durationMinutes * 60 * 1000);

    // Format to "YYYY-MM-DD HH:mm:ss"
    const formatDateTime = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    return {
      start_time: formatDateTime(startDateTime),
      end_time: formatDateTime(endDateTime),
    };
  }, [courtBookingDetails?.date, courtBookingDetails?.time, courtBookingDetails?.duration]);

  const handleBookNow = useCallback(async () => {
    try {
      setIsBooking(true);
      console.log("Court booking details:", courtBookingDetails);
      console.log("Calculated booking times:", bookingTimes);
      console.log("Court ID:", publicCourtDetails?.id);
      console.log("Sub Court ID:", selectedDetails.subCourtId);
      console.log("Booking Price ID:", selectedDetails.bookingPriceId);

      // Find court and sub-court names for display
      const courtName = publicCourtDetails?.name || "Court";
      const subCourt = publicCourtDetails?.sub_courts?.find(
        (sc: any) => sc.id === selectedDetails.subCourtId
      );
      const subCourtName = subCourt?.name || "Sub Court";

      // Save booking details to store
      const updatedCourtBookingDetails = {
        court_id: publicCourtDetails?.id,
        sub_court_id: selectedDetails.subCourtId,
        booking_price_id: selectedDetails.bookingPriceId,
        start_time: bookingTimes.start_time,
        end_time: bookingTimes.end_time,
        split: courtBookingDetails?.split || false,
        // Additional display data for checkout
        court_name: courtName,
        sub_court_name: subCourtName,
        date: courtBookingDetails?.date || "",
        time: courtBookingDetails?.time || "",
        duration: courtBookingDetails?.duration || "",
        price: (estimateData?.my_booking_base_price || 0) / 4,
        is_peak_time: pricing.isPeakTime,
      };

      console.log("Saving court booking details to store:", updatedCourtBookingDetails);
      setCourtBookingDetails(updatedCourtBookingDetails);

      // toast.success("Booking details saved! Redirecting to checkout...");
      router.push("/checkout?booking_type=court&edit=true");
    } catch (error: any) {
      console.error("Error saving booking details:", error);
      toast.error(extractErrorMessage(error) || "Failed to save booking details");
    } finally {
      setIsBooking(false);
    }
  }, [
    courtBookingDetails,
    bookingTimes,
    publicCourtDetails,
    selectedDetails,
    pricing,
    setCourtBookingDetails,
    router,
  ]);

  const estimateRequestData = useMemo(() => {
    if (!courtBookingDetails) return null;

    return {
      id: courtBookingDetails.booking_id ? courtBookingDetails.booking_id : null,
      court_id: firstCourtId,
      sub_court_id: selectedDetails.subCourtId,
      start_time: bookingTimes.start_time,
      end_time: bookingTimes.end_time,
      split: false,
      attributes: courtBookingDetails.attributes,
    };
  }, [courtBookingDetails, selectedDetails, bookingTimes, firstCourtId]);

  const {
    estimateData,
    isLoading: isEstimateDataLoading,
    error,
    refetch,
  } = useBookingEstimate(estimateRequestData);

  return (
    <div className="container mx-auto px-2 py-1">
      <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
        <div className="flex w-full flex-col items-start justify-start gap-10 rounded-[30px] bg-white p-4 lg:h-[540px] lg:flex-row lg:items-start lg:justify-between lg:p-10">
          {/* Left Section - Form Controls */}
          <div className="flex w-full flex-col items-start justify-start gap-3 lg:w-1/2">
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Court
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {courtBookingDetails?.sub_court_name || "Select court"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {publicCourtListLoading || publicCourtDetailsLoading ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading courts...
                    </span>
                  </DropdownMenuItem>
                ) : publicCourtDetails && publicCourtDetails?.sub_courts?.length > 0 ? (
                  publicCourtDetails?.sub_courts.map((court: Court) => (
                    <DropdownMenuItem
                      key={court.id}
                      onClick={() => {
                        updateBookingDetails({
                          court: court.name || `Court ${court.id}`,
                          subCourtId: court.id,
                          time: "",
                        });
                      }}
                      className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                    >
                      <span className="font-helvetica text-[15px] font-normal text-black">
                        {court.name || `Court ${court.id}`}
                      </span>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No courts available
                    </span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Date
            </div>

            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  id="date"
                  className="font-helvetica flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-6 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50"
                >
                  {date
                    ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`
                    : "Select date"}
                  <ChevronDownIcon />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto overflow-hidden border-gray-200 p-0 shadow-lg"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={date}
                  captionLayout="dropdown"
                  onSelect={(date) => {
                    const year = date!.getFullYear();
                    const month = String(date!.getMonth() + 1).padStart(2, "0");
                    const day = String(date!.getDate()).padStart(2, "0");
                    const onlyDate = `${year}-${month}-${day}`;
                    setDate(date);
                    updateBookingDetails({
                      date: onlyDate,
                      time: "",
                    });
                    setOpen(false);
                  }}
                  className="rounded-[20px] border-0 shadow-none"
                  disabled={(date) => date < new Date()}
                />
              </PopoverContent>
            </Popover>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Duration
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {courtBookingDetails?.duration || "Select duration"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {publicCourtListLoading || publicCourtDetailsLoading ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading durations...
                    </span>
                  </DropdownMenuItem>
                ) : publicCourtDetails && publicCourtDetails?.booking_prices?.length > 0 ? (
                  publicCourtDetails?.booking_prices
                    ?.sort(
                      (a: any, b: any) =>
                        a?.booking_duration?.duration_in_minutes -
                        b?.booking_duration?.duration_in_minutes
                    )
                    .map((bookingPrices: any, index: number) => (
                      <DropdownMenuItem
                        key={index}
                        onClick={() => {
                          updateBookingDetails({
                            duration: `${bookingPrices?.booking_duration?.duration_in_minutes} mins`,
                            bookingPriceId: bookingPrices.id,
                            time: "",
                          });
                        }}
                        className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                      >
                        <span className="font-helvetica text-[15px] font-normal text-black">
                          {bookingPrices?.booking_duration?.duration_in_minutes} mins
                        </span>
                      </DropdownMenuItem>
                    ))
                ) : (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No courts available
                    </span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Time
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {courtBookingDetails?.time || "Select time"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] max-w-[90vw] rounded-[20px] border border-[#c3c3c3] bg-white p-4 shadow-lg">
                {publicNextOpeningNewLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading times...
                    </span>
                  </div>
                ) : publicNextOpeningNew &&
                  publicNextOpeningNew?.all_slots &&
                  publicNextOpeningNew?.all_slots?.length > 0 ? (
                  <div className="space-y-3">
                    <div className="font-helvetica flex items-center justify-between text-sm text-gray-600">
                      <span>Available times</span>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-[#ddba0a]"></div>
                          <span className="text-xs">Peak</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-gray-300"></div>
                          <span className="text-xs">Unavailable</span>
                        </div>
                      </div>
                    </div>
                    <div className="scrollbar-hide grid max-h-[300px] grid-cols-3 gap-2 overflow-y-auto lg:grid-cols-4 xl:grid-cols-6">
                      {publicNextOpeningNew.all_slots.map((slot: any, index: number) => {
                        const isAvailable = publicNextOpeningNew?.slots?.some(
                          (availableSlot: any) => availableSlot.start === slot.start
                        );
                        const isPeak = slot.peak;

                        return (
                          <button
                            key={index}
                            onClick={() =>
                              isAvailable && updateBookingDetails({ time: slot?.start })
                            }
                            disabled={!isAvailable}
                            className={`font-helvetica relative rounded-[12px] px-3 py-2 text-sm font-normal transition-all duration-200 ${
                              isAvailable
                                ? `cursor-pointer border border-[#c3c3c3] ${courtBookingDetails?.time === slot?.start ? "border-[#1c5534] bg-[#1c5534] text-white" : "bg-white text-black hover:border-[#1c5534] hover:bg-gray-50"} `
                                : "cursor-not-allowed border border-gray-200 bg-gray-100 text-gray-400"
                            }`}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-[13px] leading-tight">{slot?.start}</span>
                              {isPeak && isAvailable && (
                                <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-[#ddba0a]"></div>
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No time slots available
                    </span>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* Divider - Hidden on mobile, visible on desktop */}
          <div
            className="hidden h-full w-[2px] rounded-full bg-[#EBEBEB] lg:block"
            data-svg-wrapper
          ></div>

          {/* Right Section - Player Controls and Booking Summary */}
          <div className="flex w-full flex-col items-start justify-between lg:w-1/2">
            <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
              {/* <div className="flex w-full items-center justify-between">
                <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                  Number of Players
                </div>
                <div className="flex items-center justify-center gap-4">
                  <button
                    onClick={() =>
                      updateBookingData("players", Math.max(1, bookingData.players - 1))
                    }
                    disabled={bookingData.players <= publicCourtDetails.min_players}
                    className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                  >
                    <Minus className="h-4 w-4 text-black" />
                  </button>
                  <div className="font-helvetica min-w-[60px] text-center text-[15px] leading-[35px] font-normal text-black">
                    {bookingData.players}
                  </div>
                  <button
                    onClick={() => updateBookingData("players", bookingData.players + 1)}
                    disabled={bookingData.players >= publicCourtDetails.max_players}
                    className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                  >
                    <Plus className="h-4 w-4 text-black" />
                  </button>
                </div>
              </div> */}

              {/* <div className="flex w-full flex-col items-start justify-start gap-2">
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                    Clutch Ai
                  </div>
                  <div className="flex items-center gap-3">
                    <InfoIcon />
                    <Switch
                      checked={bookingData.clutchAi}
                      onCheckedChange={(checked) => handleClutchAiChange(checked)}
                      className="data-[state=checked]:bg-[#ddba0a] data-[state=unchecked]:bg-gray-300"
                    />
                  </div>
                </div>
                <div className="font-helvetica w-full text-[13px] leading-snug font-normal text-black">
                  The Clutch Cam automates content creation from your courts — generating
                  Instagrammable highlight reels & performance stats for every game.
                </div>
              </div> */}

              {/* <DropdownMenu>
                <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] bg-[#fffaed] px-[25px] py-2 outline-1 outline-[#ddba0a] transition-colors outline-dashed hover:bg-[#fff8e1]">
                  <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#1c5534]">
                    {bookingData.package || "Allay Package"}
                  </span>
                  <ChevronDown className="h-4 w-12 stroke-2 text-[#ddba0a]" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border-dashed border-[#ddba0a] bg-[#fffaed] shadow-lg">
                  {["Allay Package", "Premium Package", "Basic Package", "Elite Package"].map(
                    (packageOption) => (
                      <DropdownMenuItem
                        key={packageOption}
                        onClick={() => updateBookingData("package", packageOption)}
                        className="cursor-pointer px-[25px] py-3 text-[#1c5534] hover:bg-[#fff8e1]"
                      >
                        <span className="font-helvetica text-[15px] font-normal text-[#1c5534]">
                          {packageOption}
                        </span>
                      </DropdownMenuItem>
                    )
                  )}
                </DropdownMenuContent>
              </DropdownMenu> */}
            </div>
            <div className="flex w-full flex-col items-start justify-start gap-10 py-2 lg:mt-10">
              <div className="flex w-full flex-col items-start justify-between gap-4 rounded-[20px] border-[#ddba0a] bg-[#fffaed] p-4 shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)] sm:flex-row sm:items-end sm:p-6">
                <div className="flex-1">
                  <p className="font-helvetica text-base leading-snug font-medium text-black">
                    Booking Details
                  </p>
                  {!courtBookingDetails?.sub_court_name &&
                    !courtBookingDetails?.date &&
                    !courtBookingDetails?.time &&
                    !courtBookingDetails?.duration && (
                      <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                        Please select court, date, time and duration
                      </p>
                    )}
                  {courtBookingDetails?.sub_court_name && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Court: {courtBookingDetails.sub_court_name}
                    </p>
                  )}
                  {courtBookingDetails?.date && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Date: {courtBookingDetails.date}
                    </p>
                  )}
                  {courtBookingDetails?.time && courtBookingDetails?.duration && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Time: {courtBookingDetails.time} - {courtBookingDetails.duration}
                      {pricing.isPeakTime && (
                        <span className="ml-2 inline-flex items-center rounded-full bg-[#ddba0a] px-2 py-1 text-xs font-medium text-[#1c5534]">
                          Peak
                        </span>
                      )}
                    </p>
                  )}
                  {/* <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                    {bookingData.players} Players Clutch Ai - {clutchAiStatus}
                  </p> */}
                </div>
                <div className="flex w-full flex-col items-center justify-center gap-1 py-2.5 sm:w-auto">
                  {isEstimateDataLoading ? (
                    <Skeleton className="h-10 w-20 rounded-full" />
                  ) : (
                    !error &&
                    estimateData && (
                      <div className="font-helvetica text-center text-[22px] leading-normal font-bold text-[#1c5534]">
                        Pay ${estimateData?.total.toFixed(2)}
                      </div>
                    )
                  )}
                  {/* {pricing.courtBooking > 0 && (
                    <div className="font-helvetica text-center text-[13px] leading-tight text-gray-600">
                      Court: ${pricing.courtBooking.toFixed(2)} {pricing.isPeakTime && "(Peak)"}
                      {pricing.vat > 0 && ` + VAT: $${pricing.vat.toFixed(2)}`}
                    </div>
                  )} */}
                </div>
                {error && (
                  <div className="font-helvetica text-center text-sm leading-normal text-red-500">
                    {error}
                  </div>
                )}
              </div>
              <button
                onClick={handleBookNow}
                disabled={
                  !(
                    courtBookingDetails?.sub_court_name &&
                    courtBookingDetails?.date &&
                    courtBookingDetails?.time &&
                    courtBookingDetails?.duration &&
                    estimateData &&
                    estimateData?.total > 0 &&
                    !isEstimateDataLoading &&
                    error === null
                  )
                }
                className="font-helvetica text-primary flex w-full items-center justify-center rounded-full bg-[#ddba0a] px-10 py-4 text-center text-base leading-none font-bold shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)] transition-colors hover:bg-[#c4a609] disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isBooking ? <Loader /> : "Book Now"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
