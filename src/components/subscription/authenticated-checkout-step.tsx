"use client";
import { useGetPaymentMethods } from "@/api/payment-service";
import { checkoutSubscription } from "@/api/subscription-service";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { extractErrorMessage, getCardBrandIcon } from "@/libs/utils";
import { useAuthStore } from "@/store/auth-store";
import { useSubscriptionStore } from "@/store/subscription-store";
import { Plan } from "@/types/membership";
import * as Dialog from "@radix-ui/react-dialog";
import { Check, CreditCard, Plus, X } from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import Loader from "../loader";
import { AddPaymentCardForm } from "../payments/add-payment-card-form";
import { StripeProvider } from "../providers/stripe-provider";
import { PaymentMethod, PersonalInfo } from "./types";

interface AuthenticatedCheckoutStepProps {
  selectedPlan: Plan | null;
  personalInfo: PersonalInfo;
  selectedPaymentMethod: PaymentMethod | null;
  onSelectPaymentMethod: (method: PaymentMethod) => void;
  onComplete: () => void;
}

export default function AuthenticatedCheckoutStep({
  selectedPlan,
  personalInfo,
  selectedPaymentMethod,
  onSelectPaymentMethod,
  onComplete,
}: AuthenticatedCheckoutStepProps) {
  const [showPaymentDialog, setShowPaymentDialog] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { paymentMethodList, paymentMethodLoading } = useGetPaymentMethods();
  const { token, setUser } = useAuthStore();
  const { packageAmount, isMonthly } = useSubscriptionStore();

  const handleComplete = async () => {
    try {
      setIsLoading(true);
      const payload = {
        payment_method_id: selectedPaymentMethod?.id,
        package_id: selectedPlan?.id,
        frequency: isMonthly ? "M" : "Y",
        above_18: personalInfo.isAbove18,
      };
      console.log("subscription checkout payload", payload);
      const response = await checkoutSubscription(payload);
      if (response) {
        console.log("checkoutSubscription response", response.data);
        setUser(response.data?.data?.user);
        onComplete();
      }
    } catch (error: any) {
      console.error("Error checking out subscription:", error);
      toast.error(extractErrorMessage(error) || "Error checking out subscription");
    } finally {
      setIsLoading(false);
    }
  };

  // Handler for showing payment form
  const handleShowPaymentForm = useCallback(() => {
    setShowPaymentDialog(true);
  }, []);

  // Handler for closing payment dialog
  const handleClosePaymentDialog = useCallback((open: boolean) => {
    setShowPaymentDialog(open);
  }, []);

  return (
    <div className="w-full rounded-lg bg-gradient-to-br from-white to-[#FFFAED] p-6 lg:max-w-5xl">
      <div className="mb-8 text-center">
        <h2 className="mb-2 text-gray-900">Complete Your Purchase</h2>
        <p className="text-gray-600">Review your selection and add payment method</p>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Payment Methods */}
        <div>
          <h3 className="mb-4 text-gray-900">Payment Method</h3>

          {/* Existing Payment Methods */}
          {token && !paymentMethodLoading && paymentMethodList.length > 0 && (
            <div className="mb-4 space-y-3">
              {paymentMethodList
                .sort((a: any) => (a.is_default ? -1 : 1))
                .map((method: any) => (
                  <Card
                    key={method.id}
                    className={`cursor-pointer transition-all duration-200 ${
                      selectedPaymentMethod?.id === method.id
                        ? "shadow-md ring-2 ring-[#1C5534]"
                        : "hover:shadow-md"
                    }`}
                    onClick={() => onSelectPaymentMethod(method)}
                  >
                    <CardContent className="flex items-center justify-between p-4">
                      <div className="flex items-center">
                        {method.brand?.toLowerCase() ? (
                          <img
                            src={getCardBrandIcon(method.brand)}
                            alt={`${method.brand} logo`}
                            className="h-6 w-auto"
                          />
                        ) : (
                          <CreditCard className="mr-3 h-5 w-5 text-gray-400" />
                        )}
                        <div>
                          <p className="pl-2 text-gray-900">
                            {method.brand} •••• {method.last4}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {method.is_default && (
                          <Badge variant="secondary" className="mr-2">
                            Default
                          </Badge>
                        )}
                        {selectedPaymentMethod?.id === method.id && (
                          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-[#1C5534]">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}

          {/* Add New Payment Method Button */}
          {token && (
            <div className="mb-6 flex w-full items-center justify-center">
              <button
                type="button"
                className="text-primary hover:text-primary flex items-center justify-center gap-2 disabled:cursor-not-allowed disabled:opacity-50"
                onClick={handleShowPaymentForm}
                disabled={paymentMethodLoading}
              >
                <Plus className="size-6" /> <span>Add New Card</span>
              </button>
            </div>
          )}
        </div>

        {/* Order Summary */}
        <div>
          <h3 className="mb-4 text-gray-900">Order Summary</h3>

          <Card>
            <CardContent className="p-0 lg:p-6">
              {/* Selected Plan */}
              {selectedPlan && (
                <div className="mb-6">
                  <div className="mb-2 flex items-start justify-between">
                    <div>
                      <h4 className="text-gray-900">{selectedPlan.name}</h4>
                    </div>
                    <Badge className="bg-amber-100 text-amber-800">
                      {selectedPlan.package_prices.some((p) => p.is_on_offer)
                        ? "Popular"
                        : "Selected"}
                    </Badge>
                  </div>

                  {selectedPlan.package_prices.find((p) => p.limited_members) && (
                    <p className="text-sm text-amber-600">
                      {selectedPlan.package_prices.find((p) => p.limited_members)?.limited_members}{" "}
                      spots remaining
                    </p>
                  )}
                </div>
              )}

              {/* Customer Info */}
              <div className="mb-6 rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 text-gray-900">Customer Information</h4>
                <p className="text-sm text-gray-600">{personalInfo.name}</p>
                <p className="text-sm text-gray-600">{personalInfo.email}</p>
                <p className="text-sm text-gray-600">{personalInfo.mobile}</p>
              </div>

              {/* Pricing Breakdown */}
              <div className="space-y-3 border-t pt-4">
                <div className="flex justify-between pt-3 text-lg">
                  <span className="text-gray-900">Total</span>
                  <span className="text-gray-900">${packageAmount.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Complete Purchase Button */}
          <div className="mt-6">
            <Button
              className="w-full bg-gradient-to-r from-[#1C5534] to-[#0f3a1f] py-3 text-white transition-all duration-200 hover:from-[#0f3a1f] hover:to-[#1C5534]"
              onClick={handleComplete}
              disabled={!selectedPaymentMethod}
            >
              {isLoading ? (
                <Loader className="w-full text-white" />
              ) : (
                `Complete Purchase - $${packageAmount.toFixed(2)}`
              )}
            </Button>
            {!selectedPaymentMethod && (
              <p className="mt-2 text-center text-sm text-amber-600">
                Please select a payment method to continue
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Add Payment Method Dialog */}
      <Dialog.Root open={showPaymentDialog} onOpenChange={handleClosePaymentDialog}>
        <Dialog.Portal>
          <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
          <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-[96%] max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200 lg:w-full">
            <div className="flex items-center justify-between">
              <Dialog.Title className="text-lg font-semibold">Add Payment Method</Dialog.Title>
              <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-full opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
                <X className="h-8 w-8 text-gray-600 outline-none" />
                <span className="sr-only">Close</span>
              </Dialog.Close>
            </div>

            <StripeProvider>
              <AddPaymentCardForm setOpen={handleClosePaymentDialog} />
            </StripeProvider>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  );
}
