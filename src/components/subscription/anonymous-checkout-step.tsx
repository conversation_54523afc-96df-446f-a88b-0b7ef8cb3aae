"use client";
import { addPaymentMethod, getPaymentMethods, useGetPaymentMethods } from "@/api/payment-service";
import { checkoutSubscription, createPublicIntent } from "@/api/subscription-service";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { extractErrorMessage, getCardBrandIcon } from "@/libs/utils";
import { useAuthStore } from "@/store/auth-store";
import { useSubscriptionStore } from "@/store/subscription-store";
import { Plan } from "@/types/membership";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import * as React from "react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import Loader from "../loader";
import { PaymentMethod, PersonalInfo } from "./types";
import AnonymousCheckoutStepSkeleton from "./anonymous-checkout-step-skeleton";

interface AnonymousCheckoutStepProps {
  selectedPlan: Plan | null;
  personalInfo: PersonalInfo;
  selectedPaymentMethod: PaymentMethod | null;
  onSelectPaymentMethod: (method: PaymentMethod) => void;
  onComplete: () => void;
}

export default function AnonymousCheckoutStep({
  selectedPlan,
  personalInfo,
  selectedPaymentMethod,
  onSelectPaymentMethod,
  onComplete,
}: AnonymousCheckoutStepProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = React.useState<string | null>(null);
  const [cardBrand, setCardBrand] = React.useState<string | null>(null);
  const [cardholderName, setCardholderName] = React.useState("");

  const { setToken, removeToken, setUser } = useAuthStore();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const { packageAmount, isMonthly } = useSubscriptionStore();

  const createPublicSetupIntent = async () => {
    try {
      removeToken();
      const response = await createPublicIntent({
        full_name: personalInfo.name,
        email: personalInfo.email,
        phone: personalInfo.mobile,
        country: personalInfo.countryCode,
        above_18: personalInfo.isAbove18,
      });

      if (response) {
        console.log("createPublicSetupIntent response", response.data);
        setToken(response.data?.data?.token);
        setClientSecret(response.data?.data?.client_secret);
      }
    } catch (error) {
      console.error("Error creating public setup intent:", error);
    }
  };

  useEffect(() => {
    createPublicSetupIntent();
  }, []);

  // const handleComplete = async () => {
  //   try {
  //     setIsLoading(true);
  //     const payload = {
  //       payment_method_id: selectedPaymentMethod?.id,
  //       package_id: selectedPlan?.id,
  //       frequency: isMonthly ? "M" : "Y",
  //       above_18: personalInfo.isAbove18,
  //     };
  //     console.log("subscription checkout payload", payload);
  //     const response = await checkoutSubscription(payload);
  //     if (response) {
  //       console.log("checkoutSubscription response", response.data);
  //       setUser(response.data?.data?.user);
  //       onComplete();
  //     }
  //   } catch (error: any) {
  //     console.error("Error checking out subscription:", error);
  //     toast.error(extractErrorMessage(error) || "Error checking out subscription");
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  const stripeElementStyle = {
    style: {
      base: {
        fontSize: "16px",
        fontWeight: "300",
        color: "#000000",
        "::placeholder": {
          color: "#666666",
        },
        padding: "10px 0",
      },
      invalid: {
        color: "#EF4444",
      },
    },
  };

  const stripeElementContainerStyle =
    "flex h-10 w-full rounded-full border border-gray-300 px-3 py-2 text-sm focus-within:ring-2 focus-within:ring-primary focus-within:border-transparent focus-within:outline-none";

  if (!clientSecret) {
    return <AnonymousCheckoutStepSkeleton />;
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    try {
      if (!stripe || !elements || !cardholderName.trim()) {
        setError("Please fill in all fields.");
        return;
      }

      setIsLoading(true);
      setError(null);

      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: elements.getElement(CardNumberElement)!,
        billing_details: {
          name: cardholderName.trim(),
          address: {
            country: "US",
          },
        },
      });

      if (paymentMethodError) {
        setError(paymentMethodError.message ?? "An error occurred. Please try again.");
        setIsLoading(false);
        return;
      }

      if (!clientSecret) {
        setError("Payment setup is not initialized.");
        return;
      }

      const { error: setupError } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: paymentMethod.id,
      });

      if (setupError) {
        setError(setupError.message ?? "An error occurred while confirming card setup.");
        setIsLoading(false);
        return;
      }

      if (paymentMethod) {
        const paymentMethodsResponse = await getPaymentMethods();

        console.log("paymentMethodsResponse", paymentMethodsResponse);

        if (paymentMethodsResponse && paymentMethodsResponse.data) {
          const payload = {
            payment_method_id: paymentMethodsResponse.data?.data?.[0]?.id,
            package_id: selectedPlan?.id,
            frequency: isMonthly ? "M" : "Y",
            above_18: personalInfo.isAbove18,
          };
          console.log("subscription checkout payload", payload);
          const checkoutResponse = await checkoutSubscription(payload);
          if (checkoutResponse) {
            console.log("checkoutSubscription response", checkoutResponse.data);
            setUser(checkoutResponse.data?.data?.user);
            onComplete();
          }
        }
      }
    } catch (err) {
      setError("An error occurred while saving your card. Please try again.");
      console.error("Error checking out subscription:", error);
      toast.error(extractErrorMessage(error) || "Error checking out subscription");
    } finally {
      setIsLoading(false);
    }

    setIsLoading(false);
  }

  const handleCardChange = (event: any) => {
    setCardBrand(event.brand);
  };

  return (
    <form
      className="w-full rounded-lg bg-gradient-to-br from-white to-[#FFFAED] p-6 lg:max-w-5xl"
      onSubmit={onSubmit}
    >
      <div className="mb-8 text-center">
        <h2 className="mb-2 text-gray-900">Complete Your Purchase</h2>
        <p className="text-gray-600">Add your payment information to continue</p>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Payment Methods */}
        <div>
          <h3 className="mb-4 text-gray-900">Payment Method</h3>

          {/* Add New Payment Method Form */}
          <Card className="border-2 border-dashed border-gray-300 transition-colors duration-200 hover:border-[#1C5534]">
            <CardContent className="p-6">
              {clientSecret && (
                <div onSubmit={onSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Cardholder Name
                    </label>
                    <input
                      type="text"
                      className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                      value={cardholderName}
                      onChange={(e) => setCardholderName(e.target.value)}
                      placeholder="Enter cardholder name"
                      required
                      disabled={isLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Card Number</label>
                    <div className="relative">
                      <div className={stripeElementContainerStyle}>
                        <CardNumberElement
                          options={stripeElementStyle}
                          className="h-full w-full"
                          onChange={handleCardChange}
                        />
                      </div>
                      {cardBrand && (
                        <div className="absolute top-1/2 right-3 -translate-y-1/2">
                          <img
                            src={getCardBrandIcon(cardBrand) || "/placeholder.svg"}
                            alt={`${cardBrand} logo`}
                            className="h-6 w-auto"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Expiry Date</label>
                      <div className={stripeElementContainerStyle}>
                        <CardExpiryElement options={stripeElementStyle} className="h-full w-full" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Security Code
                      </label>
                      <div className={stripeElementContainerStyle}>
                        <CardCvcElement
                          options={stripeElementStyle}
                          className="h-full w-full font-normal"
                        />
                      </div>
                    </div>
                  </div>

                  {error && <div className="mt-2 text-xs text-red-500">{error}</div>}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div>
          <h3 className="mb-4 text-gray-900">Order Summary</h3>

          <Card>
            <CardContent className="p-0 lg:p-6">
              {/* Selected Plan */}
              {selectedPlan && (
                <div className="mb-6">
                  <div className="mb-2 flex items-start justify-between">
                    <div>
                      <h4 className="text-gray-900">{selectedPlan.name}</h4>
                    </div>
                    <Badge className="bg-amber-100 text-amber-800">
                      {selectedPlan.package_prices.some((p) => p.is_on_offer)
                        ? "Popular"
                        : "Selected"}
                    </Badge>
                  </div>

                  {selectedPlan.package_prices.find((p) => p.limited_members) && (
                    <p className="text-sm text-amber-600">
                      {selectedPlan.package_prices.find((p) => p.limited_members)?.limited_members}{" "}
                      spots remaining
                    </p>
                  )}
                </div>
              )}

              {/* Customer Info */}
              <div className="mb-6 rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 text-gray-900">Customer Information</h4>
                <p className="text-sm text-gray-600">{personalInfo.name}</p>
                <p className="text-sm text-gray-600">{personalInfo.email}</p>
                <p className="text-sm text-gray-600">{personalInfo.mobile}</p>
              </div>

              {/* Pricing Breakdown */}
              <div className="space-y-3 border-t pt-4">
                <div className="flex justify-between pt-3 text-lg">
                  <span className="text-gray-900">Total</span>
                  <span className="text-gray-900">${packageAmount.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Complete Purchase Button */}
        </div>
        <div className="flex w-full justify-center">
          <Button
            className="w-full bg-gradient-to-r from-[#1C5534] to-[#0f3a1f] py-3 text-white transition-all duration-200 hover:from-[#0f3a1f] hover:to-[#1C5534]"
            type="submit"
            disabled={isLoading || !stripe || !elements}
          >
            {isLoading ? (
              <Loader className="w-full text-white" />
            ) : (
              `Complete Purchase - $${packageAmount.toFixed(2)}`
            )}
          </Button>
        </div>
      </div>
    </form>
  );
}
