"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

import { steps, existingPaymentMethods } from "./constants";
import Stepper from "./stepper";
import MembershipStep from "./membership-step";
import PersonalInfoStep from "./personal-info-step";
import CheckoutStep from "./checkout-step";
import SuccessStep from "./success-step";
import { useSubscriptionStore } from "@/store/subscription-store";
import { useAuthStore } from "@/store/auth-store";
import Loader from "../loader";
import { toast } from "sonner";
import { extractErrorMessage } from "@/libs/utils";
import { validateEmailAndPhone } from "@/api/subscription-service";
import { StripeProvider } from "../providers/stripe-provider";

export default function SubscriptionFlow() {
  const {
    currentStep,
    selectedPlan,
    personalInfo,
    selectedPaymentMethod,
    packageAmount,
    isMonthly,
    setCurrentStep,
    setSelectedPlan,
    setPersonalInfo,
    setPackageAmount,
    setSelectedPaymentMethod,
    canProceedToNextStep,
    populateUserData,
    resetSubscription,
  } = useSubscriptionStore();

  const { user, isAuthenticated } = useAuthStore();
  const [isNextButtonLoading, setIsNextButtonLoading] = useState(false);

  // Populate user data when component mounts if user is logged in
  useEffect(() => {
    if (isAuthenticated && user) {
      populateUserData({
        name: user.name,
        email: user.user_email?.email,
        phone: user.user_phone?.phone,
      });
    }
  }, [isAuthenticated, user, populateUserData]);

  const handleScrollToTop = () => {
    if (window) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const validatePersonalInfo = async () => {
    try {
      setIsNextButtonLoading(true);
      if (!user) {
        const payload = {
          email: personalInfo.email,
          phone: personalInfo.mobile,
          country: personalInfo.countryCode,
        };
        const response = await validateEmailAndPhone(payload);
        if (response.status === 200) {
          return true;
        }
        return false;
      } else {
        return true;
      }
    } catch (error: any) {
      console.error("Error validating personal info:", error);
      toast.error(extractErrorMessage(error) || "Error validating personal info");
      return false;
    } finally {
      setIsNextButtonLoading(false);
    }
  };

  const handleNext = async () => {
    if (currentStep < 4) {
      if (currentStep === 2) {
        console.log("personal info", personalInfo);
        const isValid = await validatePersonalInfo();
        if (!isValid) {
          return;
        } else {
          setCurrentStep(currentStep + 1);
          handleScrollToTop();
        }
      } else {
        setCurrentStep(currentStep + 1);
        handleScrollToTop();
      }
    }
  };

  const handleComplete = () => {
    setCurrentStep(4);
    resetSubscription();
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      handleScrollToTop();
    }
  };

  const handleStepClick = (stepId: number) => {
    // Only allow navigation to completed steps
    if (stepId < currentStep && currentStep !== 4) {
      setCurrentStep(stepId);
      handleScrollToTop();
    }
  };

  return (
    <div className="m-0 min-h-screen w-full lg:m-6" id="subscription-flow">
      <div className="w-full">
        {/* Header */}
        <div className="mb-8 px-4 text-center">
          <h1 className="mb-2 text-4xl font-bold text-gray-900">Join Epic Charlotte</h1>
          <p className="text-gray-600">Choose your membership and get started in minutes</p>
        </div>

        {/* Stepper */}
        <div className="mx-auto mb-8 max-w-4xl px-4">
          <Stepper steps={steps} currentStep={currentStep} onStepClick={handleStepClick} />
        </div>

        {/* Step Content */}
        <Card className="flex w-full justify-center rounded-none border-0">
          <CardContent className="flex w-full justify-center py-8 shadow-none hover:shadow-none">
            {currentStep === 1 && (
              <MembershipStep
                selectedPlan={selectedPlan}
                onSelectPlan={setSelectedPlan}
                onNext={handleNext}
              />
            )}
            {currentStep === 2 && (
              <PersonalInfoStep
                personalInfo={personalInfo}
                onUpdateInfo={setPersonalInfo}
                selectedPlan={selectedPlan}
              />
            )}
            {currentStep === 3 && (
              <StripeProvider>
                <CheckoutStep
                  selectedPlan={selectedPlan}
                  personalInfo={personalInfo}
                  existingPaymentMethods={existingPaymentMethods}
                  selectedPaymentMethod={selectedPaymentMethod}
                  onSelectPaymentMethod={setSelectedPaymentMethod}
                  onComplete={handleComplete}
                />
              </StripeProvider>
            )}
            {currentStep === 4 && (
              <SuccessStep
                selectedPlan={selectedPlan}
                personalInfo={personalInfo}
                selectedPaymentMethod={selectedPaymentMethod}
              />
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        {currentStep > 1 && currentStep < 4 && (
          <div className="mx-auto max-w-4xl px-4">
            <div className="mt-8 flex justify-between">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1}
                className="bg-white px-6 text-black"
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!canProceedToNextStep()}
                className={`bg-[#1C5534] px-6 text-white hover:bg-[#0f3a1f] ${currentStep === 3 ? "hidden" : "block"}`}
              >
                {isNextButtonLoading ? <Loader /> : "Continue"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
