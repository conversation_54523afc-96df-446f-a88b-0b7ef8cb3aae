"use client";

import * as React from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  cleanNumber,
  detectCardBrand,
  formatCardNumber,
  getCvvLengthForBrand,
  getMaxCardDigitsForBrand,
  isValidCvv,
  isValidExpiry,
  isValidLuhn,
  type CardBrand,
} from "@/libs/credit-card";
import { getCardBrandIcon } from "@/libs/utils";

type FieldErrors = {
  number?: string;
  expiry?: string;
  cvv?: string;
};

export function AddPaymentMethodCard() {
  const [cardNumber, setCardNumber] = React.useState("");
  const [expiry, setExpiry] = React.useState("");
  const [cvv, setCvv] = React.useState("");
  const [brand, setBrand] = React.useState<CardBrand>("unknown");
  const [errors, setErrors] = React.useState<FieldErrors>({});
  const [submitted, setSubmitted] = React.useState<null | { brand: CardBrand; last4: string }>(
    null
  );

  const digitsOnly = cleanNumber(cardNumber);
  const derivedBrand = detectCardBrand(digitsOnly);
  const maxDigits = getMaxCardDigitsForBrand(derivedBrand);
  const cvvMax = getCvvLengthForBrand(derivedBrand);

  React.useEffect(() => {
    setBrand(derivedBrand);
    // Clamp CVV length if the brand changed to one with shorter CVV
    if (cvv.length > cvvMax) {
      setCvv((prev) => prev.slice(0, cvvMax));
    }
  }, [derivedBrand, cvvMax, cvv.length]);

  function handleCardNumberChange(e: React.ChangeEvent<HTMLInputElement>) {
    const raw = e.target.value;
    let digits = cleanNumber(raw);
    const brandLocal = detectCardBrand(digits);
    const maxLocal = getMaxCardDigitsForBrand(brandLocal);
    digits = digits.slice(0, maxLocal);
    const formatted = formatCardNumber(digits, brandLocal);
    setCardNumber(formatted);
    if (errors.number) setErrors((prev) => ({ ...prev, number: undefined }));
    setSubmitted(null);
  }

  function handleExpiryChange(e: React.ChangeEvent<HTMLInputElement>) {
    let value = e.target.value.replace(/[^\d]/g, "");
    // MMYY or MMYYYY support, auto-insert slash
    if (value.length >= 3) {
      value = `${value.slice(0, 2)}/${value.slice(2, 6)}`;
    }
    // Limit to MM/YY or MM/YYYY
    if (value.length > 7) value = value.slice(0, 7);
    setExpiry(value);
    if (errors.expiry) setErrors((prev) => ({ ...prev, expiry: undefined }));
    setSubmitted(null);
  }

  function handleCvvChange(e: React.ChangeEvent<HTMLInputElement>) {
    const value = e.target.value.replace(/\D/g, "").slice(0, cvvMax);
    setCvv(value);
    if (errors.cvv) setErrors((prev) => ({ ...prev, cvv: undefined }));
    setSubmitted(null);
  }

  function validateAll(): FieldErrors {
    const newErrors: FieldErrors = {};
    const digits = cleanNumber(cardNumber);

    if (digits.length < 12) {
      newErrors.number = "Enter a valid card number.";
    } else if (!isValidLuhn(digits)) {
      newErrors.number = "Card number failed validation (Luhn check).";
    }

    if (!isValidExpiry(expiry)) {
      newErrors.expiry = "Enter a valid future expiry date in MM/YY or MM/YYYY.";
    }

    if (!isValidCvv(cvv, brand)) {
      newErrors.cvv = brand === "amex" ? "Amex CVV must be 4 digits." : "CVV must be 3 digits.";
    }

    return newErrors;
  }

  function onBlurField(field: "number" | "expiry" | "cvv") {
    const v = validateAll();
    setErrors((prev) => ({ ...prev, [field]: v[field] }));
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    const v = validateAll();
    setErrors(v);
    if (Object.keys(v).length > 0) return;

    const last4 = cleanNumber(cardNumber).slice(-4);
    // Important: Do not send the raw card data to any server here.
    // This example only demonstrates client-side validation.
    setSubmitted({ brand, last4 });
  }

  return (
    <Card className="border-2 border-dashed border-gray-300 transition-colors duration-200 hover:border-[#1C5534]">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="cardNumber">Card Number</Label>
            <div className="relative mt-1">
              <Input
                id="cardNumber"
                inputMode="numeric"
                autoComplete="cc-number"
                placeholder="1234 5678 9012 3456"
                value={cardNumber}
                onChange={handleCardNumberChange}
                onBlur={() => onBlurField("number")}
                aria-invalid={Boolean(errors.number)}
                aria-describedby={errors.number ? "cardNumber-error" : undefined}
              />
              {brand !== "unknown" && cleanNumber(cardNumber).length > 0 ? (
                <img
                  src={getCardBrandIcon(brand) || "/placeholder.svg"}
                  alt={`${brand} logo`}
                  width={36}
                  height={24}
                  className="absolute top-1/2 right-4 h-6 w-9 -translate-y-1/2 object-contain"
                />
              ) : null}
            </div>
            {errors.number && (
              <p id="cardNumber-error" className="mt-1 text-sm text-red-600">
                {errors.number}
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <Label htmlFor="expiry">Expiry Date</Label>
              <Input
                id="expiry"
                inputMode="numeric"
                autoComplete="cc-exp"
                placeholder="MM/YY"
                className="mt-1"
                value={expiry}
                onChange={handleExpiryChange}
                onBlur={() => onBlurField("expiry")}
                aria-invalid={Boolean(errors.expiry)}
                aria-describedby={errors.expiry ? "expiry-error" : undefined}
              />
              {errors.expiry && (
                <p id="expiry-error" className="mt-1 text-sm text-red-600">
                  {errors.expiry}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="cvv">CVV</Label>
              <Input
                id="cvv"
                inputMode="numeric"
                autoComplete="cc-csc"
                placeholder={brand === "amex" ? "4 digits" : "3 digits"}
                className="mt-1"
                value={cvv}
                onChange={handleCvvChange}
                onBlur={() => onBlurField("cvv")}
                aria-invalid={Boolean(errors.cvv)}
                aria-describedby={errors.cvv ? "cvv-error" : undefined}
              />
              {errors.cvv && (
                <p id="cvv-error" className="mt-1 text-sm text-red-600">
                  {errors.cvv}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* <div className="text-muted-foreground mt-6 text-xs">
          {"Security note: This example performs client-side validation (Luhn, expiry, CVV) "}
          {"only. Do not collect, transmit, or store raw card data on your servers unless "}
          {"you are PCI DSS compliant. In production, use a PCI-compliant payment processor "}
          {"that tokenizes card data in the browser."}
        </div> */}
      </CardContent>
    </Card>
  );
}
