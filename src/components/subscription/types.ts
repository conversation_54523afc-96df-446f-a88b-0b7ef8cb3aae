export interface MembershipPlan {
  id: string
  name: string
  price: number
  period: string
  description: string
  features: string[]
  isPopular?: boolean
  spotsRemaining?: number
  totalSpots?: number
}

export interface PersonalInfo {
  name: string
  mobile: string
  email: string
  countryCode: string;
  hasAgreedToWaiver: boolean
  isAbove18: boolean
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'paypal' | 'bank'
  last4?: string
  last_number?: string
  brand?: string
  isDefault?: boolean
}

export interface Step {
  id: number
  title: string
  description: string
}