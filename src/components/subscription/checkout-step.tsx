"use client";
import { useAuthStore } from "@/store/auth-store";
import { PaymentMethod, PersonalInfo } from "./types";
import { Plan } from "@/types/membership";
import AuthenticatedCheckoutStep from "./authenticated-checkout-step";
import AnonymousCheckoutStep from "./anonymous-checkout-step";

interface CheckoutStepProps {
  selectedPlan: Plan | null;
  personalInfo: PersonalInfo;
  existingPaymentMethods: PaymentMethod[];
  selectedPaymentMethod: PaymentMethod | null;
  onSelectPaymentMethod: (method: PaymentMethod) => void;
  onComplete: () => void;
}

export default function CheckoutStep({
  selectedPlan,
  personalInfo,
  selectedPaymentMethod,
  onSelectPaymentMethod,
  onComplete,
}: CheckoutStepProps) {
  const { user, isAuthenticated } = useAuthStore();

  // Render appropriate component based on authentication status
  if (isAuthenticated && user) {
    return (
      <AuthenticatedCheckoutStep
        selectedPlan={selectedPlan}
        personalInfo={personalInfo}
        selectedPaymentMethod={selectedPaymentMethod}
        onSelectPaymentMethod={onSelectPaymentMethod}
        onComplete={onComplete}
      />
    );
  }

  return (
    <AnonymousCheckoutStep
      selectedPlan={selectedPlan}
      personalInfo={personalInfo}
      selectedPaymentMethod={selectedPaymentMethod}
      onSelectPaymentMethod={onSelectPaymentMethod}
      onComplete={onComplete}
    />
  );
}
