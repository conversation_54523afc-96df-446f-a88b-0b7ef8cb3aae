"use client";
import CourtCheckout from "@/components/checkout/court-checkout";
import ProgramCheckout from "@/components/checkout/program-checkout";
import LessonCheckout from "@/components/checkout/lesson-checkout";
import InstructorCheckout from "@/components/checkout/instructor-checkout";
import { useSearchParams } from "next/navigation";
import React, { Suspense } from "react";
import CourtCheckoutEdit from "@/components/checkout/court-checkout-edit";

const CheckoutContent = () => {
  const searchParams = useSearchParams();
  const bookingType = searchParams.get("booking_type");
  const isEdit = searchParams.get("edit") === "true";

  const showCheckoutSection = () => {
    if (bookingType === "court") {
      if (isEdit) {
        return <CourtCheckoutEdit />;
      }
      return <CourtCheckout />;
    } else if (bookingType === "program") {
      return <ProgramCheckout />;
    } else if (bookingType === "lesson") {
      return <LessonCheckout />;
    } else if (bookingType === "instructor") {
      return <InstructorCheckout />;
    } else {
      return <p>Invalid booking type</p>;
    }
  };

  return (
    <>
      <h1 className="text-primary font-helvetica text-4xl font-bold">Checkout</h1>
      {showCheckoutSection()}
    </>
  );
};

const CheckoutPage = () => {
  return (
    <main className="flex min-h-screen flex-col items-center gap-8 px-2 pt-30 pb-20 lg:px-20">
      <Suspense fallback={<div>Loading...</div>}>
        <CheckoutContent />
      </Suspense>
    </main>
  );
};

export default CheckoutPage;
