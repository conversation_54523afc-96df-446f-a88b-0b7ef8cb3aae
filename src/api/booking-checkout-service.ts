import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetCourtAttributeList({ court_id }: { court_id: number }) {

    const fullUrl = useMemo(
        () => {
            const params = new URLSearchParams();
            params.append("court_id", court_id.toString());
            return `${endpoints.checkout.getCourtAttributeList}?${params.toString()}`
        }, [court_id]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetCourtAttributeList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            courtAttributeList: queueData,
            courtAttributeListLoading: isLoading,
            courtAttributeListError: error,
            courtAttributeListValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetCourtAttributeList,
    };
}

export function useGetPlayerList() {

    const fullUrl = `${endpoints.checkout.getPlayersList}`;

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPlayerList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            playerList: queueData,
            playerListLoading: isLoading,
            playerListError: error,
            playerListValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPlayerList,
    };
}


// Court attribute selection interface
export interface CheckoutCourtAttribute {
    court_attribute_id: number;
    value: number;
}

// Checkout interface matching the required structure
export interface CheckoutPayload {
    booking_id: number;
    payment_method: "APPLE_PAY" | "CARD" | "WALLET";
    payment_method_id?: number;
    use_wallet?: 0 | 1;
    court_attributes?: CheckoutCourtAttribute[];
}

// Process checkout for court booking
export async function processCheckout(checkoutData: CheckoutPayload): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.checkout, checkoutData);
    console.log("checkout response", response);
    return response;
}

export async function updateAddonsInBooking(bookingId: number, addons: CheckoutCourtAttribute[]): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.updateCourtBooking, { id: bookingId, attributes: addons });
    console.log("update addons in booking response", response);
    return response;
}

export async function addPlayerInBooking(bookingId: number, players: number[]): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.updateCourtBooking, { booking_id: bookingId, guest_users: players });
    console.log("add player in booking response", response);
    return response;
}

export async function addPlayer(playerData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.checkout.addPlayer, playerData);
    console.log("add player response", response);
    return response;
}

export async function removePlayer(playerId: number): Promise<any> {
    const response = await axiosInstance.delete(endpoints.checkout.removePlayer, { data: { friend_id: playerId } });
    console.log("API: remove player response", response);
    return response;
}

export async function applePayCourtBookingConfirm(checkoutData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.checkout.applePayConfirm, checkoutData);
    console.log("apple pay confirm response", response);
    return response;
}

export async function applePayInstructorBookingConfirm(checkoutData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.checkout.applePayInstructorConfirm, checkoutData);
    console.log("apple pay confirm response", response);
    return response;
}