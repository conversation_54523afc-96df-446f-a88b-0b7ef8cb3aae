import axiosInstance, { endpoints } from "./axios";

export async function validateEmailAndPhone(data: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.subscription.validateEmailAndPhone, data);
    return response;
}

export async function createPublicIntent(data: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.subscription.createPublicIntent, data);
    return response;
}

export async function checkoutSubscription(data: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.subscription.checkoutSubscription, data);
    return response;
}