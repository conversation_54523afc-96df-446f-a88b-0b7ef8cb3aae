import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetMyCourtBookingList({ page, limit, type }: { page: number; limit: number, type: string }) {

    const fullUrl = useMemo(
        () => {
            const params = new URLSearchParams();
            params.append("page", page.toString());
            params.append("limit", limit.toString());
            if (type) {
                params.append("type", type);
            }
            return `${endpoints.myBooking.getCourtBookingList}?${params.toString()}`;
        },
        [page, limit, type]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetMyCourtBookingList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            myCourtBookingList: queueData,
            myCourtBookingListLoading: isLoading,
            myCourtBookingListError: error,
            myCourtBookingListValidating: isValidating,
            myCourtBookingListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetMyCourtBookingList,
    };
}


export async function cancelCourtBooking(bookingId: number): Promise<any> {
    const response = await axiosInstance.delete(`${endpoints.myBooking.cancelCourtBooking}/${bookingId}`);
    return response;
}