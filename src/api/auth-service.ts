import axiosInstance, { endpoints } from "./axios";
import { signInWithApple, signInWithGoogle } from "./firebase";
import { useAuthStore } from "@/store/auth-store";

export async function googleLogin(): Promise<any> {
  const token = await signInWithGoogle();
  const response = await axiosInstance.post(endpoints.auth.social.google, {
    auth_code: token,
  });
  console.log("google response", response.data);

  if (response && response.data) {
    // Use auth store to handle login
    const { login } = useAuthStore.getState();
    login(response.data);
  }

  return response.data;
}

export async function appleLogin(): Promise<any> {
  const userData = await signInWithApple();
  const response = await axiosInstance.post(endpoints.auth.social.apple, {
    appleUserId: userData.appleUserId,
    name: userData.name || "GUEST",
    email: userData.email,
  });
  console.log("apple response", response.data);

  if (response && response.data) {
    // Use auth store to handle login
    const { login } = useAuthStore.getState();
    login(response.data);
  }

  return response.data;
}

export async function appleLoginV2(userData: any): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.social.appleV2, {
    auth_code: userData.authCode,
    name: userData.name || "GUEST",
    email: userData.email,
  });
  console.log("apple response", response.data);

  if (response && response.data) {
    // Use auth store to handle login
    const { login } = useAuthStore.getState();
    login(response.data);
  }

  return response.data;
}

export async function otpLogin({ phone = "", country = "", otp = "" }): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.login, {
    phone,
    country,
    otp,
  });
  console.log("otp response", response.data);

  if (response && response.data) {
    // Use auth store to handle login
    const { login } = useAuthStore.getState();
    login(response.data);
  }

  return response.data;
}

export async function sendOTP({ phone = "", country = "" }): Promise<any> {
  const response = await axiosInstance.post(endpoints.auth.sendOtp, {
    phone,
    country,
  });
  console.log("send otp response", response.data);
  return response.data;
}
